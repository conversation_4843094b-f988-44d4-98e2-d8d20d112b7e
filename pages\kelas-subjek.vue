<template>
  <!-- Loading State -->
  <div v-if="loading" class="space-y-8">
    <!-- <PERSON> Header Skeleton -->
    <div class="space-y-4">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div class="space-y-2">
          <div class="h-8 bg-gray-200 dark:bg-gray-700 rounded-md w-48 animate-pulse"></div>
          <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded-md w-80 animate-pulse"></div>
        </div>
        <div class="h-10 bg-gray-200 dark:bg-gray-700 rounded-md w-48 animate-pulse"></div>
      </div>
    </div>

    <!-- Search Card Skeleton -->
    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
      <div class="h-10 bg-gray-200 dark:bg-gray-700 rounded-md w-full animate-pulse"></div>
    </div>

    <!-- Table Skeleton -->
    <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
      <!-- Table Header -->
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <div class="h-6 bg-gray-200 dark:bg-gray-700 rounded-md w-48 animate-pulse"></div>
          <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded-md w-24 animate-pulse"></div>
        </div>
      </div>

      <!-- Table Content -->
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th class="px-6 py-3">
                <div class="h-4 bg-gray-200 dark:bg-gray-600 rounded w-4 animate-pulse"></div>
              </th>
              <th class="px-6 py-3">
                <div class="h-4 bg-gray-200 dark:bg-gray-600 rounded w-16 animate-pulse"></div>
              </th>
              <th class="px-6 py-3">
                <div class="h-4 bg-gray-200 dark:bg-gray-600 rounded w-24 animate-pulse"></div>
              </th>
              <th class="px-6 py-3">
                <div class="h-4 bg-gray-200 dark:bg-gray-600 rounded w-20 animate-pulse"></div>
              </th>
              <th class="px-6 py-3">
                <div class="h-4 bg-gray-200 dark:bg-gray-600 rounded w-20 animate-pulse"></div>
              </th>
              <th class="px-6 py-3">
                <div class="h-4 bg-gray-200 dark:bg-gray-600 rounded w-16 animate-pulse"></div>
              </th>
              <th class="px-6 py-3">
                <div class="h-4 bg-gray-200 dark:bg-gray-600 rounded w-20 animate-pulse"></div>
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
            <tr v-for="i in 5" :key="i">
              <td class="px-6 py-4">
                <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-4 animate-pulse"></div>
              </td>
              <td class="px-6 py-4">
                <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-16 animate-pulse"></div>
              </td>
              <td class="px-6 py-4">
                <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-12 animate-pulse"></div>
              </td>
              <td class="px-6 py-4">
                <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-24 animate-pulse"></div>
              </td>
              <td class="px-6 py-4">
                <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-8 animate-pulse"></div>
              </td>
              <td class="px-6 py-4">
                <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-6 animate-pulse"></div>
              </td>
              <td class="px-6 py-4">
                <div class="flex space-x-2">
                  <div class="h-6 bg-gray-200 dark:bg-gray-700 rounded w-6 animate-pulse"></div>
                  <div class="h-6 bg-gray-200 dark:bg-gray-700 rounded w-6 animate-pulse"></div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <div v-if="!loading" class="space-y-8">
    <!-- Page Header -->
    <UiCompositePageHeader title="Kelas & Subjek" subtitle="Urus maklumat kelas dan subjek yang diajar"
      icon="heroicons:academic-cap-solid">
      <template #actions>
        <UiBaseButton variant="primary" size="sm" sm:size="md" prepend-icon="heroicons:plus-solid" @click="openAddModal"
          class="flex-1 sm:flex-none">
          <span class="hidden sm:inline">Tambah Kelas & Subjek</span>
          <span class="sm:hidden">Tambah</span>
        </UiBaseButton>
      </template>
    </UiCompositePageHeader>

    <!-- Search and Filter Section -->
    <UiCompositeCard v-if="classSubjects.length > 0">
      <template #default>
        <div class="flex flex-col sm:flex-row gap-4">
          <div class="flex-1">
            <UiBaseInput v-model="searchQuery" placeholder="Cari kelas atau subjek..."
              prepend-icon="heroicons:magnifying-glass-solid" @input="handleSearch" />
          </div>
          <UiBaseButton v-if="searchQuery" variant="outline" size="sm" @click="clearSearch"
            prepend-icon="heroicons:x-mark-solid">
            Kosongkan
          </UiBaseButton>
        </div>
      </template>
    </UiCompositeCard>

    <!-- Data Table -->
    <UiCompositeCard>
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <UiBaseIcon name="heroicons:table-cells-solid" class="w-5 h-5 text-primary" />
            <h2 class="text-xl font-semibold">Senarai Kelas & Subjek</h2>
          </div>
          <div v-if="filteredClassSubjects.length > 0" class="text-sm text-gray-500 dark:text-gray-400">
            {{ filteredClassSubjects.length }} daripada {{ classSubjects.length }} rekod
          </div>
        </div>
      </template>
      <template #default>
        <!-- Table for desktop -->
        <div v-if="filteredClassSubjects.length > 0" class="hidden md:block overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
              <tr>
                <!-- Bulk Select Checkbox -->
                <th scope="col" class="px-6 py-3 text-center">
                  <UiBaseCheckbox id="select-all-checkbox" :model-value="isAllSelected" :indeterminate="isIndeterminate"
                    @update:model-value="toggleSelectAll" />
                </th>
                <th scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                  Tahap
                </th>
                <th scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                  Nama Kelas
                </th>
                <th scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                  Subjek
                </th>
                <th scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                  Singkatan
                </th>
                <th scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                  Bil. Murid
                </th>
                <th scope="col"
                  class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                  Tindakan
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
              <tr v-for="(item, index) in filteredClassSubjects" :key="`${item.class_id}-${item.subject_id}-${index}`">
                <!-- Row Checkbox -->
                <td class="px-6 py-4 whitespace-nowrap text-center">
                  <UiBaseCheckbox :id="`checkbox-${index}`" :model-value="selectedItems.includes(getItemKey(item))"
                    @update:model-value="(checked) => toggleItemSelection(item, checked)" />
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                  {{ getClassLevelLabel(item.class_id) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                  {{ item.className }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                  {{ getSubjectName(item.subject_id) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                  {{ item.subject_abbreviation ?? '-' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                  {{ item.studentCount ?? '-' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                  <UiBaseTooltip text="Kemaskini">
                    <UiBaseIcon name="ph:pencil-simple" size="18px" @click="openEditModal(item)"
                      class="cursor-pointer text-primary hover:text-primary-focus dark:text-primary-dark dark:hover:text-primary-dark-focus p-1 rounded-md" />
                  </UiBaseTooltip>
                  <UiBaseTooltip text="Padam">
                    <UiBaseIcon name="ph:trash" size="18px" @click="openDeleteConfirmation(item)"
                      class="cursor-pointer text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-500 p-1 rounded-md" />
                  </UiBaseTooltip>
                </td>
              </tr>
            </tbody>
          </table>

          <!-- Bulk Delete Footer -->
          <div v-if="selectedItems.length > 0"
            class="bg-gray-50 dark:bg-gray-700 px-6 py-3 border-t border-gray-200 dark:border-gray-600">
            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-700 dark:text-gray-300">
                {{ selectedItems.length }} item{{ selectedItems.length > 1 ? 's' : '' }} dipilih
              </span>
              <UiBaseButton variant="delete" prepend-icon="mdi:delete" @click="openBulkDeleteModal">
                Padam Terpilih
              </UiBaseButton>
            </div>
          </div>
        </div>

        <!-- Cards for mobile -->
        <div v-if="filteredClassSubjects.length > 0" class="md:hidden space-y-4">
          <div v-for="(item, index) in filteredClassSubjects"
            :key="`mobile-${item.class_id}-${item.subject_id}-${index}`"
            class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 space-y-3">
            <div class="flex justify-between items-start">
              <div class="flex items-start space-x-3">
                <!-- Mobile Checkbox -->
                <UiBaseCheckbox :id="`mobile-checkbox-${index}`" :model-value="selectedItems.includes(getItemKey(item))"
                  @update:model-value="(checked) => toggleItemSelection(item, checked)" custom-class="mt-1" />
                <div>
                  <h3 class="font-medium text-gray-900 dark:text-gray-100">{{ item.className }}</h3>
                  <p class="text-sm text-gray-500 dark:text-gray-400">{{ getClassLevelLabel(item.class_id) }}</p>
                </div>
              </div>
              <div class="flex space-x-2">
                <UiBaseIcon name="ph:pencil-simple" size="18px" @click="openEditModal(item)"
                  class="cursor-pointer text-primary hover:text-primary-focus p-1 rounded-md" />
                <UiBaseIcon name="ph:trash" size="18px" @click="openDeleteConfirmation(item)"
                  class="cursor-pointer text-red-600 hover:text-red-700 p-1 rounded-md" />
              </div>
            </div>
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span class="text-gray-500 dark:text-gray-400">Subjek:</span>
                <p class="font-medium text-gray-900 dark:text-gray-100">{{ getSubjectName(item.subject_id) }}</p>
              </div>
              <div>
                <span class="text-gray-500 dark:text-gray-400">Singkatan:</span>
                <p class="font-medium text-gray-900 dark:text-gray-100">{{ item.subject_abbreviation ?? '-' }}</p>
              </div>
              <div>
                <span class="text-gray-500 dark:text-gray-400">Bil. Murid:</span>
                <p class="font-medium text-gray-900 dark:text-gray-100">{{ item.studentCount ?? '-' }}</p>
              </div>
            </div>
          </div>

          <!-- Mobile Bulk Delete Footer -->
          <div v-if="selectedItems.length > 0" class="mt-4 bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <div class="flex flex-col sm:flex-row justify-between items-center space-y-2 sm:space-y-0">
              <span class="text-sm text-gray-700 dark:text-gray-300">
                {{ selectedItems.length }} item{{ selectedItems.length > 1 ? 's' : '' }} dipilih
              </span>
              <UiBaseButton variant="alert-error" prepend-icon="mdi:delete" @click="openBulkDeleteModal"
                class="w-full sm:w-auto">
                Padam Terpilih
              </UiBaseButton>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div v-if="filteredClassSubjects.length === 0 && classSubjects.length === 0" class="text-center py-12">
          <UiBaseIcon name="heroicons:academic-cap-solid" class="mx-auto h-12 w-12 text-gray-400" />
          <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">Tiada kelas & subjek</h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Mulakan dengan menambah kelas dan subjek pertama
            anda.</p>
          <div class="mt-6">
            <UiBaseButton variant="primary" @click="openAddModal" prepend-icon="heroicons:plus-solid">
              Tambah Kelas & Subjek
            </UiBaseButton>
          </div>
        </div>

        <!-- No Search Results -->
        <div v-if="filteredClassSubjects.length === 0 && classSubjects.length > 0" class="text-center py-12">
          <UiBaseIcon name="heroicons:magnifying-glass-solid" class="mx-auto h-12 w-12 text-gray-400" />
          <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">Tiada keputusan ditemui</h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Cuba ubah kata kunci carian anda.</p>
          <div class="mt-6">
            <UiBaseButton variant="outline" @click="clearSearch">
              Kosongkan Carian
            </UiBaseButton>
          </div>
        </div>
      </template>
    </UiCompositeCard>



    <!-- Internal Modal for Add/Edit (EXACT copy of ClassSubject.vue modal) -->
    <UiCompositeModal ref="modalRef" :is-open="isModalOpen" :title="modalTitle" @update:is-open="isModalOpen = $event">
      <form @submit.prevent="saveClassSubject" class="space-y-4 p-4">
        <div class="flex space-x-2 mb-4">
          <UiBaseButton @click="selectLevelType('Tahun')"
            :variant="selectedLevelType === 'Tahun' ? 'primary' : 'outline'" type="button" class="flex-1">
            Tahun
          </UiBaseButton>
          <UiBaseButton @click="selectLevelType('Tingkatan')"
            :variant="selectedLevelType === 'Tingkatan' ? 'primary' : 'outline'" type="button" class="flex-1">
            Tingkatan
          </UiBaseButton>
        </div>
        <div>
          <UiBaseSingleSelect id="class-select" v-model="formData.class" :options="classOptions" option-label="label"
            option-value="value" placeholder="Sila pilih kelas" @update:modelValue="handleClassSelection"
            :aria-invalid="!!formErrors?.class" aria-describedby="class-error" :disabled="isFormDisabled" />
          <span v-if="formErrors?.class" id="class-error" class="text-alert-error text-sm mt-1">{{
            formErrors.class[0] }}</span>
        </div>

        <div v-if="formData.class && existingClassOptions.length > 0">
          <UiBaseSingleSelect id="existing-class-select" v-model="selectedExistingClassName"
            :options="existingClassOptions" option-label="label" option-value="value"
            placeholder="Pilih nama kelas sedia ada (jika ada)" @update:modelValue="handleExistingClassSelection"
            :disabled="isFormDisabled || !formData.class" />
        </div>

        <div>
          <UiBaseInput id="class-name-input" v-model="formData.className"
            placeholder="Nama Kelas (Cth: Tahun 1, 2 Bestari)" @update:modelValue="handleClassNameInput"
            :aria-invalid="!!formErrors?.className" aria-describedby="className-error" :disabled="isFormDisabled" />
          <span v-if="formErrors?.className" id="className-error" class="text-alert-error text-sm mt-1">{{
            formErrors.className[0] }}</span>
        </div>

        <div>
          <UiBaseInput id="student-count-input" v-model.number="formData.studentCount" type="number"
            placeholder="Jumlah Murid" @update:modelValue="handleStudentCountInput"
            :aria-invalid="!!formErrors?.studentCount" aria-describedby="studentCount-error"
            :disabled="isFormDisabled" />
          <span v-if="formErrors?.studentCount" id="studentCount-error" class="text-alert-error text-sm mt-1">{{
            formErrors.studentCount[0] }}</span>
        </div>

        <div>
          <UiBaseSingleSelect id="subject-select" v-model="formData.subject" :options="computedSubjectOptions"
            option-label="label" option-value="value" show-search
            :placeholder="!formData.class ? 'Pilih Kelas dahulu' : 'Sila pilih subjek'"
            @dropdown-did-open="handleDropdownDidOpen" @dropdown-state-changed="handleDropdownStateChange"
            @update:modelValue="handleSubjectSelection" :aria-invalid="!!formErrors?.subject"
            aria-describedby="subject-error" :disabled="isFormDisabled || subjectsLoading || !formData.class">
            <template #customOptionLabel="{ option }">
              <div v-if="option.value === ADD_NEW_SUBJECT_VALUE" class="flex items-center">
                <UiBaseIcon name="mdi:plus" class="mr-2 h-5 w-5" />
                <span>Tambah Subjek Baru...</span>
              </div>
            </template>
          </UiBaseSingleSelect>
          <span v-if="formErrors?.subject && formData.subject !== ADD_NEW_SUBJECT_VALUE" id="subject-error"
            class="text-alert-error text-sm mt-1">{{
              formErrors.subject[0] }}</span>
        </div>

        <div v-if="showNewSubjectInput" class="space-y-2 mt-2">
          <UiBaseInput id="new-subject-input" v-model="newSubjectNameFromInput" placeholder="Taip nama subjek baru"
            :disabled="isFormDisabled || subjectsLoading" />
          <UiBaseButton @click="addNewSubject" variant="secondary" type="button"
            :disabled="isFormDisabled || subjectsLoading || !newSubjectNameFromInput.trim()">
            Simpan Subjek Baru
          </UiBaseButton>
          <span v-if="formErrors?.subject && formData.subject === ADD_NEW_SUBJECT_VALUE" id="new-subject-error"
            class="text-alert-error text-sm mt-1">{{ formErrors.subject[0] }}</span>
        </div>

        <div>
          <UiBaseInput id="subject-abbreviation-input" v-model="formData.subjectAbbreviation"
            placeholder="Singkatan nama subjek (cth: BM, BI, MAT)" @update:modelValue="handleSubjectAbbreviationInput"
            :aria-invalid="!!formErrors?.subjectAbbreviation" aria-describedby="subjectAbbreviation-error"
            :disabled="isFormDisabled" />
          <span v-if="formErrors?.subjectAbbreviation" id="subjectAbbreviation-error"
            class="text-alert-error text-sm mt-1">{{
              formErrors.subjectAbbreviation[0] }}</span>
        </div>

        <div class="flex flex-col-reverse gap-y-2 sm:flex-row sm:justify-end sm:gap-y-0 sm:space-x-3 mt-6">
          <UiBaseButton type="button" @click="cancelModal" variant="outline">
            Batal
          </UiBaseButton>
          <UiBaseButton type="submit" variant="primary"
            :disabled="isFormDisabled || subjectsLoading || !selectedLevelType">
            {{ isEditing ? 'Kemaskini' : 'Simpan' }}
          </UiBaseButton>
        </div>
      </form>
    </UiCompositeModal>

    <!-- Delete Confirmation Modal -->
    <UiCompositeDeleteConfirmationModal :is-open="isDeleteModalOpen"
      :title="deleteItem ? `Padam ${deleteItem.className}` : 'Padam Kelas & Subjek'"
      :confirmation-message="deleteItem ? `Adakah anda pasti ingin memadam kelas '${deleteItem.className}' untuk subjek '${getSubjectName(deleteItem.subject_id)}'?` : ''"
      warning-message="Tindakan ini tidak boleh dibatalkan." item-type="kelas & subjek"
      :item-name="deleteItem?.className" @confirm="handleDelete" @cancel="closeDeleteConfirmation"
      :loading="isDeleting" />

    <!-- Bulk Delete Confirmation Modal -->
    <UiCompositeDeleteConfirmationModal :is-open="isBulkDeleteModalOpen" title="Padam Kelas & Subjek Terpilih"
      :confirmation-message="getBulkDeleteMessage()" item-type="kelas & subjek" :item-name="getBulkDeleteItemList()"
      @confirm="handleBulkDelete" @cancel="closeBulkDeleteModal" :loading="isBulkDeleting" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { useSupabaseUser, useSupabaseClient } from '#imports'
import { useToast } from '~/composables/useToast'
import type { UserClassSubjectEntry } from '~/schemas/userSchemas'

// Components
import UiCompositePageHeader from '~/components/ui/composite/PageHeader.vue'
import UiCompositeCard from '~/components/ui/composite/Card.vue'
import UiBaseButton from '~/components/ui/base/Button.vue'
import UiBaseIcon from '~/components/ui/base/Icon.vue'
import UiBaseInput from '~/components/ui/base/Input.vue'
import UiBaseTooltip from '~/components/ui/base/Tooltip.vue'
import UiBaseCheckbox from '~/components/ui/base/Checkbox.vue'
import UiCompositeDeleteConfirmationModal from '~/components/ui/composite/DeleteConfirmationModal.vue'
import UiCompositeModal from '~/components/ui/composite/Modal.vue'
import UiBaseSingleSelect from '~/components/ui/base/SingleSelect.vue'



// Define page meta
definePageMeta({
  layout: 'default'
})

// Set page head
useHead({
  title: 'Kelas & Subjek - EduPlan Pro',
  meta: [
    {
      name: 'description',
      content: 'Urus maklumat kelas dan subjek yang diajar untuk perancangan yang lebih baik.'
    }
  ]
})

// =====================================================
// COMPOSABLES & STATE
// =====================================================

const user = useSupabaseUser()
const client = useSupabaseClient()
const { success: showSuccessToast, error: showErrorToast } = useToast()
const { subjects, loading: subjectsLoading, fetchSubjects, addSubject: addSubjectToDb } = useSubjects()

// =====================================================
// REACTIVE STATE
// =====================================================

const loading = ref(true)
const classSubjects = ref<UserClassSubjectEntry[]>([])
const searchQuery = ref('')

// Internal Modal State (like ClassSubject)
const isModalOpen = ref(false)
const modalRef = ref<InstanceType<typeof UiCompositeModal> | null>(null)
const editingId = ref<number | null>(null)
const isEditing = ref(false)

// Form State (like ClassSubject)
const selectedLevelType = ref<'Tahun' | 'Tingkatan' | null>(null)
const formData = ref({
  id: null as number | null,
  class: null as string | null,
  className: '',
  studentCount: null as number | null,
  subject: null as string | null,
  subjectAbbreviation: ''
})
const formErrors = ref<Record<string, string[]> | null>(null)

// Subject related state
const ADD_NEW_SUBJECT_VALUE = 'add_new_subject_option_value'
const newSubjectNameFromInput = ref('')
const showNewSubjectInput = ref(false)

// Store session-only subjects locally within this component instance (EXACT copy from ClassSubject)
const sessionOnlySubjects = ref<Subject[]>([])

// "Kelas sedia ada" state
const selectedExistingClassName = ref<string | null>(null)

// Category labels for better UX (EXACT copy from ClassSubject)
const CATEGORY_LABELS: Record<string, string> = {
  'teras': 'Teras',
  'tambahan': 'Tambahan',
  'pilihan': 'Pilihan',
  'elektif_sains': 'Elektif Sains',
  'elektif_sastera': 'Elektif Sastera'
}

// Helper function to get class level from class value (EXACT copy from ClassSubject)
const getClassLevel = (classValue: string): string | null => {
  if (!classValue) return null
  if (classValue.startsWith('t')) return 'tahun'
  if (classValue.startsWith('f')) {
    const level = parseInt(classValue.substring(1))
    if (level >= 1 && level <= 3) return 'lower'
    if (level >= 4 && level <= 5) return 'upper'
  }
  return null
}

// Saving state
const isSaving = ref(false)

// Dropdown state for auto-scroll (EXACT copy from ClassSubject)
const isAdvancedSelectDropdownOpen = ref(false)

// Delete confirmation state
const isDeleteModalOpen = ref(false)
const deleteItem = ref<UserClassSubjectEntry | null>(null)
const isDeleting = ref(false)

// Bulk delete state
const selectedItems = ref<string[]>([])
const isBulkDeleteModalOpen = ref(false)
const isBulkDeleting = ref(false)

// =====================================================
// COMPUTED PROPERTIES
// =====================================================

const filteredClassSubjects = computed(() => {
  if (!searchQuery.value.trim()) {
    return classSubjects.value
  }

  const query = searchQuery.value.toLowerCase().trim()
  return classSubjects.value.filter(item => {
    const className = item.className?.toLowerCase() || ''
    const subjectName = getSubjectName(item.subject_id)?.toLowerCase() || ''
    const abbreviation = item.subject_abbreviation?.toLowerCase() || ''

    return className.includes(query) ||
      subjectName.includes(query) ||
      abbreviation.includes(query)
  })
})

// Bulk selection computed properties
const isAllSelected = computed(() => {
  return filteredClassSubjects.value.length > 0 &&
    selectedItems.value.length === filteredClassSubjects.value.length
})

const isIndeterminate = computed(() => {
  return selectedItems.value.length > 0 &&
    selectedItems.value.length < filteredClassSubjects.value.length
})

// =====================================================
// COMPUTED PROPERTIES (like ClassSubject)
// =====================================================

// Class options based on selected level type (FIXED: Use actual data format)
const classOptions = computed(() => {
  if (selectedLevelType.value === 'Tahun') {
    return [
      { label: 'Tahun 1', value: 't1' },
      { label: 'Tahun 2', value: 't2' },
      { label: 'Tahun 3', value: 't3' },
      { label: 'Tahun 4', value: 't4' },
      { label: 'Tahun 5', value: 't5' },
      { label: 'Tahun 6', value: 't6' }
    ]
  }
  if (selectedLevelType.value === 'Tingkatan') {
    return [
      { label: 'Tingkatan 1', value: 'f1' },
      { label: 'Tingkatan 2', value: 'f2' },
      { label: 'Tingkatan 3', value: 'f3' },
      { label: 'Tingkatan 4', value: 'f4' },
      { label: 'Tingkatan 5', value: 'f5' }
    ]
  }
  return []
})

// Existing class options (EXACT copy from ClassSubject)
const existingClassOptions = computed(() => {
  if (!formData.value.class) return []
  const selectedClassValue = formData.value.class
  // Filter unique class names for the selected class level from the current list
  const uniqueClassNames = new Set(
    classSubjects.value
      .filter(item => item.class_id === selectedClassValue && item.className)
      .map(item => item.className)
  )
  return Array.from(uniqueClassNames).map(name => ({ value: name, label: name }))
})

// Subject options with categorization and filtering (EXACT copy from ClassSubject)
const computedSubjectOptions = computed(() => {
  // Combine subjects from DB (via useSubjects) and session-only subjects
  let combinedSubjects = [...subjects.value, ...sessionOnlySubjects.value]

  // Filter subjects based on selected level and class
  if (selectedLevelType.value && formData.value.class) {
    const levelType = selectedLevelType.value.toLowerCase()
    const classLevel = getClassLevel(formData.value.class)

    combinedSubjects = combinedSubjects.filter(subject => {
      // Include custom subjects
      if (subject.is_custom) return true

      // Filter by level_type
      if (subject.level_type !== levelType && subject.level_type !== 'both') return false

      // For tingkatan, also filter by sub_level
      if (levelType === 'tingkatan' && classLevel && subject.sub_level) {
        if (subject.sub_level !== 'all' && subject.sub_level !== classLevel) return false
      }

      return subject.is_active !== false // Include if is_active is true or undefined
    })
  }

  // Group subjects by category and sort
  const groupedSubjects = combinedSubjects.reduce((groups, subject) => {
    const category = subject.category || 'lain-lain'
    if (!groups[category]) groups[category] = []
    groups[category].push(subject)
    return groups
  }, {} as Record<string, typeof combinedSubjects>)

  // Sort subjects within each category by sort_order then name
  Object.keys(groupedSubjects).forEach(category => {
    groupedSubjects[category].sort((a, b) => {
      if (a.sort_order !== b.sort_order) {
        return (a.sort_order || 999) - (b.sort_order || 999)
      }
      return a.name.localeCompare(b.name)
    })
  })

  // Create options with category headers
  const options: any[] = []
  const categoryOrder = ['teras', 'tambahan', 'pilihan', 'elektif_sains', 'elektif_sastera', 'lain-lain']

  categoryOrder.forEach(category => {
    if (groupedSubjects[category] && groupedSubjects[category].length > 0) {
      // Add category header (disabled option)
      options.push({
        value: `header_${category}`,
        label: `--- ${CATEGORY_LABELS[category] || category.toUpperCase()} ---`,
        code: '',
        disabled: true
      })

      // Add subjects in this category
      groupedSubjects[category].forEach(subject => {
        options.push({
          value: subject.id,
          label: subject.name,
          code: subject.code
        })
      })
    }
  })

  // Add "Tambah Subjek Baru..." option at the end
  if (options.length > 0) {
    options.push({ value: ADD_NEW_SUBJECT_VALUE, label: '+ Tambah Subjek Baru...', code: '' })
  }

  return options
})

// Modal title
const modalTitle = computed(() => {
  if (!selectedLevelType.value) return 'Sila Pilih Tahun/Tingkatan Dahulu'
  return isEditing.value ? 'Kemaskini Kelas & Subjek' : 'Tambah Kelas & Subjek'
})

// Form disabled state
const isFormDisabled = computed(() => !selectedLevelType.value || subjectsLoading.value)

// =====================================================
// HELPER FUNCTIONS
// =====================================================

const getClassLevelLabel = (classId: string): string => {
  if (!classId) return '-'

  if (classId.startsWith('t')) {
    const level = classId.substring(1)
    return `Tahun ${level}`
  } else if (classId.startsWith('f')) {
    const level = classId.substring(1)
    return `Tingkatan ${level}`
  }

  return classId
}

const getSubjectName = (subjectId: string | null): string => {
  if (!subjectId) return '-'

  const subject = subjects.value.find(s => s.id === subjectId)
  return subject?.name || 'Subjek Tidak Diketahui'
}

const getItemKey = (item: UserClassSubjectEntry): string => {
  return `${item.class_id}-${item.subject_id}`
}

const getBulkDeleteMessage = (): string => {
  if (selectedItems.value.length === 0) return ''

  return `Adakah anda pasti ingin memadam kelas yang terpilih?`
}

const getBulkDeleteItemList = (): string => {
  if (selectedItems.value.length === 0) return ''

  const selectedClassSubjects = classSubjects.value.filter(item =>
    selectedItems.value.includes(getItemKey(item))
  )

  return selectedClassSubjects.map(item =>
    `• ${item.className} - ${getSubjectName(item.subject_id)}`
  ).join('\n')
}

// =====================================================
// DATA MANAGEMENT
// =====================================================

const fetchClassSubjects = async () => {
  if (!user.value) return

  try {
    loading.value = true

    // Use the same pattern as useSubjects.ts
    const { data: profileData, error: profileError } = await (client as any)
      .from('profiles')
      .select('class_subjects')
      .eq('id', user.value.id)
      .single()

    if (profileError) throw profileError

    if (profileData?.class_subjects) {
      // Handle different possible types for class_subjects (same as useSubjects.ts)
      let classSubjectsData: any[] = []
      if (Array.isArray(profileData.class_subjects)) {
        classSubjectsData = profileData.class_subjects
      } else if (profileData.class_subjects && typeof profileData.class_subjects === 'object') {
        // Convert object to array if it's a single object
        classSubjectsData = [profileData.class_subjects]
      }

      classSubjects.value = classSubjectsData as UserClassSubjectEntry[]

      // Fetch subject details for all subjects
      const subjectIds = classSubjects.value
        .map(cs => cs.subject_id)
        .filter(Boolean) as string[]

      if (subjectIds.length > 0) {
        await fetchSubjects(subjectIds)
      }
    }
  } catch (err) {
    console.error('Error fetching class subjects:', err)
    showErrorToast('Gagal memuatkan data kelas & subjek')
  } finally {
    loading.value = false
  }
}

const saveClassSubjects = async (data: UserClassSubjectEntry[]) => {
  if (!user.value) return false

  try {
    isSaving.value = true

    // Use the same pattern as other parts of the codebase
    const { error } = await (client as any)
      .from('profiles')
      .update({ class_subjects: data })
      .eq('id', user.value.id)

    if (error) throw error

    classSubjects.value = [...data]
    return true
  } catch (err) {
    console.error('Error saving class subjects:', err)
    showErrorToast('Gagal menyimpan kelas & subjek')
    return false
  } finally {
    isSaving.value = false
  }
}

// =====================================================
// DROPDOWN HANDLERS (EXACT copy from ClassSubject)
// =====================================================

// =====================================================
// FORM HELPER FUNCTIONS (like ClassSubject)
// =====================================================

const resetForm = () => {
  // For add modal, clear everything including level type
  // For edit modal, this function is not used (we set state directly)
  selectedLevelType.value = null
  formData.value = {
    id: null,
    class: null,
    className: '',
    studentCount: null,
    subject: null,
    subjectAbbreviation: ''
  }
  formErrors.value = null
  showNewSubjectInput.value = false
  newSubjectNameFromInput.value = ''
  selectedExistingClassName.value = null
}

// =====================================================
// FORM FUNCTIONS (like ClassSubject)
// =====================================================

// Level type selection
const selectLevelType = (type: 'Tahun' | 'Tingkatan') => {
  if (selectedLevelType.value === type) {
    selectedLevelType.value = null
    formData.value.class = null
  } else {
    selectedLevelType.value = type
    formData.value.class = null
  }
  formData.value.className = ''
  formData.value.subject = null
  formData.value.subjectAbbreviation = ''
  formData.value.studentCount = null
  formErrors.value = null
  showNewSubjectInput.value = false
  newSubjectNameFromInput.value = ''
  selectedExistingClassName.value = null
}

// Class selection handler
const handleClassSelection = (value: string | null) => {
  formData.value.class = value
  selectedExistingClassName.value = null // Reset existing class name selection
  if (!value) {
    formData.value.className = ''
    formData.value.subject = null
    formData.value.studentCount = null
    showNewSubjectInput.value = false
    newSubjectNameFromInput.value = ''
  }
  // validateField('class') - TODO: Add validation if needed
}

// Existing class selection handler (EXACT copy from ClassSubject)
const handleExistingClassSelection = (value: string | null) => {
  selectedExistingClassName.value = value
  if (value) {
    formData.value.className = value
    // validateField('className') - TODO: Add validation if needed

    // Pre-fill student count if this class name exists in classSubjects (EXACT copy from ClassSubject)
    const existingEntry = classSubjects.value.find(
      item => item.class_id === formData.value.class && item.className === value
    )
    if (existingEntry) {
      formData.value.studentCount = existingEntry.studentCount ?? null
      // validateField('studentCount') - TODO: Add validation if needed
    } else {
      formData.value.studentCount = null // Reset if no matching entry
    }
  }
}

// Class name input handler
const handleClassNameInput = (value: string) => {
  formData.value.className = value
  // validateField('className') - TODO: Add validation if needed
}

// Student count input handler
const handleStudentCountInput = (value: number | null) => {
  formData.value.studentCount = value
  // validateField('studentCount') - TODO: Add validation if needed
}

// Subject selection handler
const handleSubjectSelection = (value: string | null) => {
  formData.value.subject = value
  if (value === ADD_NEW_SUBJECT_VALUE) {
    showNewSubjectInput.value = true
    formData.value.subjectAbbreviation = ''
  } else {
    showNewSubjectInput.value = false
    newSubjectNameFromInput.value = ''
    // Auto-fill abbreviation if subject is selected
    const selectedSubject = subjects.value.find(s => s.id === value)
    if (selectedSubject) {
      formData.value.subjectAbbreviation = selectedSubject.code || ''
    }
  }
  // validateField('subject') - TODO: Add validation if needed
}

// Subject abbreviation input handler
const handleSubjectAbbreviationInput = (value: string) => {
  formData.value.subjectAbbreviation = value
  // validateField('subjectAbbreviation') - TODO: Add validation if needed
}

// Dropdown handlers (EXACT copy from ClassSubject)
const handleDropdownDidOpen = (payload: any) => {
  // Only auto-scroll for subject dropdown
  if (isAdvancedSelectDropdownOpen.value && modalRef.value && payload && payload.actualHeight) {
    // Wait for dropdown to render, then scroll modal to bottom
    nextTick(() => {
      const modalScrollArea = modalRef.value?.modalScrollArea
      if (modalScrollArea && typeof modalScrollArea.scrollTo === 'function') {
        modalScrollArea.scrollTo({ top: modalScrollArea.scrollHeight, behavior: 'smooth' })
      }
    })
  }
}

const handleDropdownStateChange = (isOpen: boolean) => {
  isAdvancedSelectDropdownOpen.value = isOpen
}

// Add new subject function
const addNewSubject = async () => {
  if (!newSubjectNameFromInput.value.trim()) return

  try {
    const newSubject = await addSubjectToDb({
      name: newSubjectNameFromInput.value.trim(),
      code: newSubjectNameFromInput.value.trim().toLowerCase().replace(/[^a-z0-9\s]/g, '').replace(/\s+/g, '-').substring(0, 10)
    })

    if (newSubject) {
      // Select the newly created subject
      formData.value.subject = newSubject.id
      showNewSubjectInput.value = false
      newSubjectNameFromInput.value = ''

      // Refresh subjects list
      await fetchSubjects()
    }
  } catch (error) {
    console.error('Error adding new subject:', error)
    showErrorToast('Gagal menambah subjek baru')
  }
}

// =====================================================
// INTERNAL MODAL FUNCTIONS (like ClassSubject)
// =====================================================

// Add function - opens internal modal for new entry
const openAddModal = () => {
  isEditing.value = false
  editingId.value = null

  // Clear ALL state for fresh start
  selectedLevelType.value = null
  formData.value = {
    id: null,
    class: null,
    className: '',
    studentCount: null,
    subject: null,
    subjectAbbreviation: ''
  }
  formErrors.value = null
  showNewSubjectInput.value = false
  newSubjectNameFromInput.value = ''
  selectedExistingClassName.value = null

  isModalOpen.value = true
}

// Edit function - opens internal modal with pre-populated data (EXACT copy of ClassSubject handleEdit)
const openEditModal = (item: UserClassSubjectEntry) => {
  // Use the SAME tahunOptions and tingkatanOptions as ClassOptions computed property (FIXED: Correct format)
  const tahunOptionsForEdit = [
    { label: 'Tahun 1', value: 't1' },
    { label: 'Tahun 2', value: 't2' },
    { label: 'Tahun 3', value: 't3' },
    { label: 'Tahun 4', value: 't4' },
    { label: 'Tahun 5', value: 't5' },
    { label: 'Tahun 6', value: 't6' }
  ]
  const tingkatanOptionsForEdit = [
    { label: 'Tingkatan 1', value: 'f1' },
    { label: 'Tingkatan 2', value: 'f2' },
    { label: 'Tingkatan 3', value: 'f3' },
    { label: 'Tingkatan 4', value: 'f4' },
    { label: 'Tingkatan 5', value: 'f5' }
  ]

  // EXACT same logic as ClassSubject handleEdit
  const isTahun = tahunOptionsForEdit.some(opt => opt.value === item.class_id)
  const isTingkatan = tingkatanOptionsForEdit.some(opt => opt.value === item.class_id)

  // Set level type FIRST (EXACT copy from ClassSubject)
  if (isTahun) {
    selectedLevelType.value = 'Tahun'
  } else if (isTingkatan) {
    selectedLevelType.value = 'Tingkatan'
  } else {
    selectedLevelType.value = null
  }

  // Use nextTick to ensure classOptions are updated before setting formData (EXACT copy from ClassSubject)
  nextTick(() => {
    formData.value = {
      id: Date.now(), // Internal ID for editing tracking
      class: item.class_id,
      className: item.className,
      studentCount: item.studentCount ?? null,
      subject: item.subject_id,
      subjectAbbreviation: item.subject_abbreviation || ''
    }
    isEditing.value = true
    editingId.value = Date.now() // Store internal ID of item being edited
    formErrors.value = null
    showNewSubjectInput.value = false
    newSubjectNameFromInput.value = ''
    selectedExistingClassName.value = item.className // Pre-select if className exists (EXACT copy from ClassSubject)
    isModalOpen.value = true
  })
}

// Cancel modal
const cancelModal = () => {
  isModalOpen.value = false
  resetForm()
}



// Save function (handles both add and edit)
const saveClassSubject = async () => {
  if (!formData.value.class || !formData.value.className || !formData.value.subject || !formData.value.subjectAbbreviation) {
    return
  }

  // Check for duplicates
  const isDuplicate = classSubjects.value.some(item =>
    item.class_id === formData.value.class &&
    item.className === formData.value.className &&
    item.subject_id === formData.value.subject &&
    (!isEditing.value || item.class_id !== formData.value.class || item.subject_id !== formData.value.subject)
  )

  if (isDuplicate) {
    showErrorToast('Kelas dan subjek ini sudah wujud')
    return
  }

  const newEntry: UserClassSubjectEntry = {
    class_id: formData.value.class,
    className: formData.value.className,
    studentCount: formData.value.studentCount,
    subject_id: formData.value.subject,
    subject_abbreviation: formData.value.subjectAbbreviation
  }

  let updatedClassSubjects: UserClassSubjectEntry[]

  if (isEditing.value) {
    // Update existing item
    updatedClassSubjects = classSubjects.value.map(item => {
      // Find the item being edited (this is tricky since we don't have a unique ID)
      // We'll match based on the original data before editing
      if (editingId.value && item.class_id === formData.value.class && item.subject_id === formData.value.subject) {
        return newEntry
      }
      return item
    })
  } else {
    // Add new item
    updatedClassSubjects = [...classSubjects.value, newEntry]
  }

  // Save to database
  const success = await saveClassSubjects(updatedClassSubjects)
  if (success) {
    isModalOpen.value = false
    resetForm()
    showSuccessToast(isEditing.value ? 'Kelas & subjek berjaya dikemaskini' : 'Kelas & subjek berjaya ditambah')
  }
}



// =====================================================
// DELETE FUNCTIONALITY
// =====================================================

const openDeleteConfirmation = (item: UserClassSubjectEntry) => {
  deleteItem.value = item
  isDeleteModalOpen.value = true
}

const closeDeleteConfirmation = () => {
  deleteItem.value = null
  isDeleteModalOpen.value = false
}

const handleDelete = async () => {
  if (!deleteItem.value) return

  try {
    isDeleting.value = true

    const updatedClassSubjects = classSubjects.value.filter(item =>
      !(item.class_id === deleteItem.value!.class_id &&
        item.subject_id === deleteItem.value!.subject_id)
    )

    const success = await saveClassSubjects(updatedClassSubjects)
    if (success) {
      showSuccessToast('Kelas & subjek berjaya dipadam')
      closeDeleteConfirmation()
    }
  } catch (err) {
    console.error('Error deleting class subject:', err)
    showErrorToast('Gagal memadam kelas & subjek')
  } finally {
    isDeleting.value = false
  }
}

// =====================================================
// BULK SELECTION FUNCTIONALITY
// =====================================================

const toggleSelectAll = () => {
  if (isAllSelected.value) {
    selectedItems.value = []
  } else {
    selectedItems.value = filteredClassSubjects.value.map(item => getItemKey(item))
  }
}

const toggleItemSelection = (item: UserClassSubjectEntry, checked: boolean) => {
  const itemKey = getItemKey(item)
  if (checked) {
    if (!selectedItems.value.includes(itemKey)) {
      selectedItems.value.push(itemKey)
    }
  } else {
    selectedItems.value = selectedItems.value.filter(key => key !== itemKey)
  }
}

const openBulkDeleteModal = () => {
  isBulkDeleteModalOpen.value = true
}

const closeBulkDeleteModal = () => {
  isBulkDeleteModalOpen.value = false
}

const handleBulkDelete = async () => {
  if (selectedItems.value.length === 0) return

  try {
    isBulkDeleting.value = true

    // Filter out selected items
    const updatedClassSubjects = classSubjects.value.filter(item => {
      const itemKey = getItemKey(item)
      return !selectedItems.value.includes(itemKey)
    })

    const success = await saveClassSubjects(updatedClassSubjects)
    if (success) {
      showSuccessToast(`${selectedItems.value.length} kelas & subjek berjaya dipadam`)
      selectedItems.value = []
      closeBulkDeleteModal()
    }
  } catch (err) {
    console.error('Error bulk deleting class subjects:', err)
    showErrorToast('Gagal memadam kelas & subjek terpilih')
  } finally {
    isBulkDeleting.value = false
  }
}

// =====================================================
// SEARCH FUNCTIONALITY
// =====================================================

const handleSearch = () => {
  // Search is reactive through computed property
}

const clearSearch = () => {
  searchQuery.value = ''
}

// =====================================================
// LIFECYCLE
// =====================================================

onMounted(async () => {
  await fetchClassSubjects()
})

// Watch for user changes
watch(user, async (newUser) => {
  if (newUser) {
    await fetchClassSubjects()
  } else {
    classSubjects.value = []
  }
})
</script>
