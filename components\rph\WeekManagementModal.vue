<template>
  <UiCompositeModal :is-open="isOpen" title="Urus Minggu" @close="$emit('close')" size="lg" :z-index="zIndex">
    <div class="space-y-4">
      <!-- Header with search and actions -->
      <div class="space-y-3">
        <!-- Search input -->
        <div class="flex-1 w-full">
          <UiBaseInput v-model="searchQuery" placeholder="Cari minggu..."
            prepend-icon="heroicons:magnifying-glass-solid" class="w-full" />
        </div>

        <!-- Bulk action bar (only visible when weeks are selected) -->
        <div v-if="hasSelectedWeeks"
          class="flex items-center justify-between p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg">
          <div class="flex items-center space-x-2">
            <UiBaseIcon name="heroicons:check-circle-solid" class="w-4 h-4 text-red-600" />
            <span class="text-sm font-medium text-red-700 dark:text-red-300">
              {{ selectedWeeksForDeletion.size }} minggu dipilih
            </span>
          </div>
          <UiBaseButton @click="$emit('openBulkDeleteModal')" variant="alert-error" size="sm"
            prepend-icon="heroicons:trash">
            Padam
          </UiBaseButton>
        </div>
      </div>

      <!-- Results summary -->
      <div class="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
        <span v-if="searchQuery">
          Menunjukkan {{ filteredWeeks.length }} daripada {{ sortedWeeks.length }} minggu
        </span>
        <span v-else>
          Jumlah minggu: {{ sortedWeeks.length }}
        </span>
        <div v-if="filteredWeeks.length > 10" class="flex items-center space-x-1">
          <UiBaseIcon name="heroicons:information-circle-solid" class="w-4 h-4" />
          <span class="text-xs">Scroll untuk lihat semua</span>
        </div>
      </div>

      <!-- Empty states -->
      <div v-if="sortedWeeks.length === 0" class="text-center py-12">
        <UiBaseIcon name="heroicons:calendar-solid" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <p class="text-gray-500 dark:text-gray-400 mb-4">Tiada minggu untuk diurus</p>
        <UiBaseButton @click="$emit('openAddWeekModal')" variant="primary" size="sm"
          prepend-icon="heroicons:plus-solid">
          Tambah Minggu Pertama
        </UiBaseButton>
      </div>

      <div v-else-if="filteredWeeks.length === 0" class="text-center py-12">
        <UiBaseIcon name="heroicons:magnifying-glass-solid" class="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <p class="text-gray-500 dark:text-gray-400 mb-2">Tiada minggu dijumpai untuk "{{ searchQuery }}"</p>
        <p class="text-sm text-gray-400">Cuba cari dengan kata kunci lain</p>
      </div>

      <!-- Week list with virtual scrolling area -->
      <div v-else class="space-y-2">
        <!-- Quick actions for large lists -->
        <div v-if="filteredWeeks.length > 20"
          class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
          <div class="flex items-center space-x-2 text-sm">
            <UiBaseIcon name="heroicons:light-bulb-solid" class="w-4 h-4 text-blue-600" />
            <span class="text-blue-800 dark:text-blue-200">Tip: Gunakan carian untuk cari minggu dengan cepat</span>
          </div>
        </div>

        <!-- Select All Header -->
        <div v-if="filteredWeeks.length > 0" @click="handleSelectAllRowClick"
          class="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors cursor-pointer select-none">
          <!-- Selection checkbox -->
          <div class="flex-shrink-0" @click.stop>
            <UiBaseCheckbox id="select-all-weeks" :model-value="allWeeksSelected"
              :indeterminate="hasSelectedWeeks && !allWeeksSelected" @update:model-value="handleSelectAllCheckboxUpdate"
              class="rounded" />
          </div>
          <span class="text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer flex-1">
            Pilih Semua Minggu ({{ filteredWeeks.length }} minggu)
          </span>
        </div>

        <!-- Scrollable week list -->
        <div class="max-h-80 overflow-y-auto space-y-2 border border-gray-200 dark:border-gray-700 rounded-lg p-2">
          <div v-for="(week, index) in filteredWeeks" :key="`manage-${week.id}`"
            class="group flex items-center justify-between p-3 border border-gray-100 dark:border-gray-800 rounded-lg hover:border-gray-300 dark:hover:border-gray-600 transition-all duration-200"
            :class="{
              'border-l-4 border-l-primary bg-primary/5': week.id === selectedWeekId,
              'border-l-4 border-l-yellow-400 bg-yellow-50 dark:bg-yellow-900/20': selectedWeeksForDeletion.has(week.id),
              'hover:bg-gray-50 dark:hover:bg-gray-800': !selectedWeeksForDeletion.has(week.id)
            }">
            <div class="flex items-center space-x-3 min-w-0 flex-1 cursor-pointer select-none"
              @click="toggleWeekSelection(week.id)">
              <!-- Selection checkbox (always visible) -->
              <div class="flex-shrink-0" @click.stop>
                <UiBaseCheckbox :id="`week-select-${week.id}`" :model-value="selectedWeeksForDeletion.has(week.id)"
                  @update:model-value="(value: boolean) => toggleWeekSelection(week.id, value)" class="rounded" />
              </div>

              <div class="flex-shrink-0 w-8 text-center">
                <span class="text-xs font-medium text-gray-500 dark:text-gray-400">
                  {{ week.week_number || index + 1 }}
                </span>
              </div>
              <div class="min-w-0 flex-1">
                <span class="font-medium text-gray-900 dark:text-white block truncate">{{ week.name }}</span>
              </div>
            </div>

            <!-- Individual delete button -->
            <div class="flex-shrink-0 ml-3" @click.stop>
              <UiBaseButton @click="$emit('openDeleteWeekModal', week)" variant="alert-error" size="sm"
                prepend-icon="heroicons:trash"
                class="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <span class="hidden sm:inline">Padam</span>
              </UiBaseButton>
            </div>
          </div>
        </div>

        <!-- Pagination info for large lists -->
        <div v-if="filteredWeeks.length > 50" class="text-center py-2">
          <p class="text-xs text-gray-500">
            Menunjukkan semua {{ filteredWeeks.length }} minggu.
            <button @click="searchQuery = ''" class="text-primary hover:underline">Papar semua</button>
          </p>
        </div>
      </div>
    </div>
  </UiCompositeModal>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { RphWeek } from '~/types/rph'
import UiCompositeModal from '~/components/ui/composite/Modal.vue'
import UiBaseButton from '~/components/ui/base/Button.vue'
import UiBaseIcon from '~/components/ui/base/Icon.vue'
import UiBaseInput from '~/components/ui/base/Input.vue'
import UiBaseCheckbox from '~/components/ui/base/Checkbox.vue'

interface Props {
  isOpen: boolean
  sortedWeeks: RphWeek[]
  selectedWeekId: string | null
  selectedWeeksForDeletion: Set<string>
  searchQuery: string
  zIndex?: number
}

const props = defineProps<Props>()

const emit = defineEmits<{
  close: []
  openAddWeekModal: []
  openBulkDeleteModal: []
  openDeleteWeekModal: [week: RphWeek]
  'update:searchQuery': [value: string]
  'update:selectedWeeksForDeletion': [value: Set<string>]
}>()

// Computed models for two-way binding
const searchQuery = computed({
  get: () => props.searchQuery,
  set: (value) => emit('update:searchQuery', value)
})

// Multi-select functionality
const filteredWeeks = computed(() => {
  if (!searchQuery.value.trim()) {
    return props.sortedWeeks
  }

  const query = searchQuery.value.toLowerCase().trim()
  return props.sortedWeeks.filter(week =>
    week.name.toLowerCase().includes(query) ||
    (week.week_number && week.week_number.toString().includes(query))
  )
})

const hasSelectedWeeks = computed(() => props.selectedWeeksForDeletion.size > 0)

const allWeeksSelected = computed(() => {
  return filteredWeeks.value.length > 0 &&
    filteredWeeks.value.every(week => props.selectedWeeksForDeletion.has(week.id))
})

const toggleWeekSelection = (weekId: string, value?: boolean) => {
  const selected = new Set(props.selectedWeeksForDeletion)

  // If value is provided (from checkbox update), use it; otherwise toggle
  const shouldSelect = value !== undefined ? value : !selected.has(weekId)

  if (shouldSelect) {
    selected.add(weekId)
  } else {
    selected.delete(weekId)
  }
  emit('update:selectedWeeksForDeletion', selected)
}

const handleSelectAllCheckboxUpdate = (value: boolean) => {
  // When checkbox is clicked directly, set explicit value
  if (value) {
    emit('update:selectedWeeksForDeletion', new Set(filteredWeeks.value.map(week => week.id)))
  } else {
    emit('update:selectedWeeksForDeletion', new Set())
  }
}

const handleSelectAllRowClick = () => {
  // When row is clicked, toggle based on current state
  if (allWeeksSelected.value) {
    emit('update:selectedWeeksForDeletion', new Set())
  } else {
    emit('update:selectedWeeksForDeletion', new Set(filteredWeeks.value.map(week => week.id)))
  }
}
</script>
