<template>
  <Teleport to="body">
    <div v-if="toasts.length > 0"
      class="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 space-y-2 pointer-events-none w-[95vw] max-w-md sm:max-w-sm"
      aria-live="polite" aria-label="Notifikasi">
      <TransitionGroup name="toast-list" tag="div" class="space-y-2">
        <ToastItem v-for="toast in toasts" :key="toast.id" :toast="toast" @close="removeToast(toast.id)"
          class="pointer-events-auto" />
      </TransitionGroup>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { useToast } from '~/composables/useToast'
import ToastItem from './ToastItem.vue'

const { toasts, removeToast } = useToast()
</script>

<style scoped>
.toast-list-enter-active {
  transition: all 0.3s ease-out;
}

.toast-list-leave-active {
  transition: all 0.2s ease-in;
}

.toast-list-enter-from {
  transform: translateY(-30px);
  opacity: 0;
}

.toast-list-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

.toast-list-move {
  transition: transform 0.3s ease;
}
</style>
