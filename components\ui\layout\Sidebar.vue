<template>
    <!-- Desktop Sidebar -->
    <aside
        class="hidden lg:flex lg:flex-col lg:w-64 lg:fixed lg:inset-y-0 bg-white dark:bg-dark-card border-r border-gray-200 dark:border-dark-border">
        <!-- Sidebar Header -->
        <div class="flex items-center h-16 px-6 border-b border-gray-200 dark:border-dark-border">
            <div class="flex items-center space-x-3">
                <div
                    class="w-8 h-8 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center">
                    <Icon name="heroicons:academic-cap-solid" class="w-5 h-5 text-white" />
                </div>
                <div>
                    <h2 class="text-sm font-semibold text-gray-900 dark:text-white">EduPlan Pro</h2>
                    <p class="text-xs text-gray-500 dark:text-gray-400">Dashboard</p>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <nav class="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
            <!-- Main Navigation -->
            <div class="space-y-1">
                <h3 class="px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-3">
                    Main
                </h3>
                <UiLayoutSidebarLink to="/maklumat-guru" icon="heroicons:user-solid"
                    :active="route.path === '/maklumat-guru'">
                    Maklumat Guru
                </UiLayoutSidebarLink>
                <UiLayoutSidebarLink to="/" icon="heroicons:home-solid" :active="route.path === '/'">
                    Dashboard
                </UiLayoutSidebarLink>
                <UiLayoutSidebarLink to="/items" icon="heroicons:rectangle-stack-solid"
                    :active="route.path === '/items'" :badge="itemsCount">
                    Items
                </UiLayoutSidebarLink>
                <UiLayoutSidebarLink to="/classes" icon="heroicons:users-solid" :active="route.path === '/classes'">
                    Classes
                </UiLayoutSidebarLink>
                <UiLayoutSidebarLink to="/kelas-subjek" icon="heroicons:academic-cap-solid"
                    :active="route.path === '/kelas-subjek'">
                    Kelas & Subjek
                </UiLayoutSidebarLink>
                <UiLayoutSidebarLink to="/assignments" icon="heroicons:document-text-solid"
                    :active="route.path === '/assignments'">
                    Assignments
                </UiLayoutSidebarLink>
                <UiLayoutSidebarLink to="/tugas-guru" icon="heroicons:clipboard-document-list-solid"
                    :active="route.path === '/tugas-guru'">
                    Tugas Guru
                </UiLayoutSidebarLink>
                <UiLayoutSidebarLink to="/rph" icon="heroicons:document-arrow-up-solid" :active="route.path === '/rph'">
                    RPH
                </UiLayoutSidebarLink>
                <UiLayoutSidebarLink to="/rpt" icon="heroicons:document-chart-bar-solid"
                    :active="route.path === '/rpt'">
                    RPT
                </UiLayoutSidebarLink>
                <UiLayoutSidebarLink to="/schedule" icon="heroicons:calendar-solid"
                    :active="route.path === '/schedule'">
                    Jadual Mengajar
                </UiLayoutSidebarLink>
                <UiLayoutSidebarLink to="/refleksi" icon="heroicons:chat-bubble-left-ellipsis-solid"
                    :active="route.path === '/refleksi'">
                    Refleksi
                </UiLayoutSidebarLink>
                <UiLayoutSidebarLink to="/dskp" icon="heroicons:document-text-solid" :active="route.path === '/dskp'">
                    DSKP
                </UiLayoutSidebarLink>
                <UiLayoutSidebarLink to="/takwim-tahunan" icon="heroicons:calendar-days-solid"
                    :active="route.path === '/takwim-tahunan'">
                    Takwim Tahunan
                </UiLayoutSidebarLink>
                <UiLayoutSidebarLink to="/jadual-pencerapan" icon="heroicons:eye-solid"
                    :active="route.path === '/jadual-pencerapan'">
                    Jadual Pencerapan
                </UiLayoutSidebarLink>
            </div>

            <!-- Development Section -->
            <div class="pt-6 space-y-1">
                <h3 class="px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-3">
                    Development
                </h3>
                <UiLayoutSidebarLink to="/components" icon="heroicons:puzzle-piece-solid"
                    :active="route.path === '/components'">
                    Components
                </UiLayoutSidebarLink>
                <UiLayoutSidebarLink to="/ui" icon="heroicons:paint-brush-solid" :active="route.path === '/ui'">
                    UI Elements
                </UiLayoutSidebarLink>
            </div>

            <!-- User Section -->
            <div class="pt-6 space-y-1">
                <h3 class="px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-3">
                    Account
                </h3>
                <UiLayoutSidebarLink to="/profile" icon="heroicons:user-solid" :active="route.path === '/profile'">
                    Profile
                </UiLayoutSidebarLink>
                <UiLayoutSidebarLink to="/settings" icon="heroicons:cog-6-tooth-solid"
                    :active="route.path === '/settings'">
                    Settings
                </UiLayoutSidebarLink>
            </div>
        </nav>

        <!-- Sidebar Footer -->
        <div class="p-4 border-t border-gray-200 dark:border-dark-border">
            <div class="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                <div
                    class="w-8 h-8 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center">
                    <Icon name="heroicons:user-solid" class="w-4 h-4 text-white" />
                </div>
                <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {{ user?.email?.split('@')[0] || 'User' }}
                    </p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">Online</p>
                </div>
                <Button variant="outline" size="sm" @click="$emit('logout')">
                    <Icon name="heroicons:arrow-right-on-rectangle-solid" class="w-4 h-4" />
                </Button>
            </div>
        </div>
    </aside>

    <!-- Mobile Sidebar Overlay -->
    <div v-if="isMobileOpen" class="lg:hidden">
        <!-- Background overlay -->
        <div class="fixed inset-0 bg-black bg-opacity-50 z-40" @click="$emit('close')"></div>

        <!-- Sidebar panel -->
        <div
            class="fixed inset-y-0 left-0 w-64 bg-white dark:bg-dark-card border-r border-gray-200 dark:border-dark-border z-50 transform transition-transform duration-300 ease-in-out">
            <!-- Mobile Header -->
            <div class="flex items-center justify-between h-16 px-6 border-b border-gray-200 dark:border-dark-border">
                <div class="flex items-center space-x-3">
                    <div
                        class="w-8 h-8 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center">
                        <Icon name="heroicons:academic-cap-solid" class="w-5 h-5 text-white" />
                    </div>
                    <div>
                        <h2 class="text-sm font-semibold text-gray-900 dark:text-white">EduPlan Pro</h2>
                        <p class="text-xs text-gray-500 dark:text-gray-400">Dashboard</p>
                    </div>
                </div>
                <Button variant="outline" size="sm" @click="$emit('close')">
                    <Icon name="heroicons:x-mark-solid" class="w-5 h-5" />
                </Button>
            </div>

            <!-- Mobile Navigation -->
            <nav class="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
                <!-- Main Navigation -->
                <div class="space-y-1">
                    <h3
                        class="px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-3">
                        Main
                    </h3>
                    <UiLayoutSidebarLink to="/" icon="heroicons:home-solid" :active="route.path === '/'"
                        @click="$emit('close')">
                        Dashboard
                    </UiLayoutSidebarLink>
                    <UiLayoutSidebarLink to="/items" icon="heroicons:rectangle-stack-solid"
                        :active="route.path === '/items'" :badge="itemsCount" @click="$emit('close')">
                        Items
                    </UiLayoutSidebarLink>
                    <UiLayoutSidebarLink to="/classes" icon="heroicons:users-solid" :active="route.path === '/classes'"
                        @click="$emit('close')">
                        Classes
                    </UiLayoutSidebarLink>
                    <UiLayoutSidebarLink to="/kelas-subjek" icon="heroicons:academic-cap-solid"
                        :active="route.path === '/kelas-subjek'" @click="$emit('close')">
                        Kelas & Subjek
                    </UiLayoutSidebarLink>
                    <UiLayoutSidebarLink to="/assignments" icon="heroicons:document-text-solid"
                        :active="route.path === '/assignments'" @click="$emit('close')">
                        Assignments
                    </UiLayoutSidebarLink>
                    <UiLayoutSidebarLink to="/tugas-guru" icon="heroicons:clipboard-document-list-solid"
                        :active="route.path === '/tugas-guru'" @click="$emit('close')">
                        Tugas Guru
                    </UiLayoutSidebarLink>
                    <UiLayoutSidebarLink to="/rph" icon="heroicons:document-arrow-up-solid"
                        :active="route.path === '/rph'" @click="$emit('close')">
                        RPH
                    </UiLayoutSidebarLink>
                    <UiLayoutSidebarLink to="/rpt" icon="heroicons:document-chart-bar-solid"
                        :active="route.path === '/rpt'" @click="$emit('close')">
                        RPT
                    </UiLayoutSidebarLink>
                    <UiLayoutSidebarLink to="/dskp" icon="heroicons:document-text-solid"
                        :active="route.path === '/dskp'" @click="$emit('close')">
                        DSKP
                    </UiLayoutSidebarLink>
                    <UiLayoutSidebarLink to="/maklumat-guru" icon="heroicons:user-solid"
                        :active="route.path === '/maklumat-guru'" @click="$emit('close')">
                        Maklumat Guru
                    </UiLayoutSidebarLink>
                    <UiLayoutSidebarLink to="/takwim-tahunan" icon="heroicons:calendar-days-solid"
                        :active="route.path === '/takwim-tahunan'" @click="$emit('close')">
                        Takwim Tahunan
                    </UiLayoutSidebarLink>
                    <UiLayoutSidebarLink to="/jadual-pencerapan" icon="heroicons:eye-solid"
                        :active="route.path === '/jadual-pencerapan'" @click="$emit('close')">
                        Jadual Pencerapan
                    </UiLayoutSidebarLink>
                </div>

                <!-- Development Section -->
                <div class="pt-6 space-y-1">
                    <h3
                        class="px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-3">
                        Development
                    </h3>
                    <UiLayoutSidebarLink to="/components" icon="heroicons:puzzle-piece-solid"
                        :active="route.path === '/components'" @click="$emit('close')">
                        Components
                    </UiLayoutSidebarLink>
                    <UiLayoutSidebarLink to="/ui" icon="heroicons:paint-brush-solid" :active="route.path === '/ui'"
                        @click="$emit('close')">
                        UI Elements
                    </UiLayoutSidebarLink>
                </div>

                <!-- User Section -->
                <div class="pt-6 space-y-1">
                    <h3
                        class="px-3 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-3">
                        Account
                    </h3>
                    <UiLayoutSidebarLink to="/profile" icon="heroicons:user-solid" :active="route.path === '/profile'"
                        @click="$emit('close')">
                        Profile
                    </UiLayoutSidebarLink>
                    <UiLayoutSidebarLink to="/settings" icon="heroicons:cog-6-tooth-solid"
                        :active="route.path === '/settings'" @click="$emit('close')">
                        Settings
                    </UiLayoutSidebarLink>
                </div>
            </nav>

            <!-- Mobile Footer -->
            <div class="p-4 border-t border-gray-200 dark:border-dark-border">
                <div class="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                    <div
                        class="w-8 h-8 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center">
                        <Icon name="heroicons:user-solid" class="w-4 h-4 text-white" />
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                            {{ user?.email?.split('@')[0] || 'User' }}
                        </p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">Online</p>
                    </div>
                    <Button variant="outline" size="sm" @click="$emit('logout')">
                        <Icon name="heroicons:arrow-right-on-rectangle-solid" class="w-4 h-4" />
                    </Button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import Button from '~/components/ui/base/Button.vue';

// Props
interface Props {
    isMobileOpen?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    isMobileOpen: false
});

// Emits
const emit = defineEmits<{
    close: []
    logout: []
}>();

// Get current user
const user = useSupabaseUser();
const route = useRoute();

// Mock data for badges (you can replace with real data)
const itemsCount = ref(12);
</script>
